<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lucide图标修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .icon-test {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-align: center;
        }
        .icon-test svg {
            display: block;
            margin: 0 auto 5px;
        }
        .results {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Lucide图标修复测试</h1>
    
    <div class="test-section">
        <h2>1. 加载Lucide库测试</h2>
        <button onclick="testLucideLibrary()">测试Lucide库加载</button>
        <div id="lucide-test-results" class="results"></div>
    </div>

    <div class="test-section">
        <h2>2. 问题图标测试</h2>
        <button onclick="testProblemIcons()">测试问题图标</button>
        <div id="problem-icons-container"></div>
        <div id="problem-icons-results" class="results"></div>
    </div>

    <div class="test-section">
        <h2>3. 图标渲染函数测试</h2>
        <button onclick="testIconRendering()">测试图标渲染</button>
        <div id="icon-rendering-container"></div>
        <div id="icon-rendering-results" class="results"></div>
    </div>

    <!-- 加载Lucide库 -->
    <script src="js/lib/lucide.js"></script>
    
    <script>
        // 测试结果更新函数
        function updateTestResults(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent += message + '\n';
        }

        // 1. 测试Lucide库加载
        function testLucideLibrary() {
            const resultsId = 'lucide-test-results';
            document.getElementById(resultsId).textContent = '';
            
            updateTestResults(resultsId, '=== Lucide库加载测试 ===');
            
            // 检查Lucide库是否加载
            if (typeof lucide !== 'undefined') {
                updateTestResults(resultsId, '✓ Lucide图标库加载成功');
                
                // 检查Lucide库的内容
                const iconNames = Object.keys(lucide).filter(key => 
                    typeof lucide[key] !== 'function' && Array.isArray(lucide[key])
                );
                updateTestResults(resultsId, `✓ Lucide库包含 ${iconNames.length} 个图标`);
                
                // 测试几个常用图标
                const testIcons = ['Star', 'Heart', 'Home', 'User'];
                testIcons.forEach(iconName => {
                    if (lucide[iconName]) {
                        updateTestResults(resultsId, `✓ 找到图标: ${iconName}`);
                    } else {
                        updateTestResults(resultsId, `✗ 未找到图标: ${iconName}`);
                    }
                });
                
                // 测试问题图标的别名
                const problemIcons = {
                    'CircleStop': 'stop',
                    'ShoppingBag': 'shop', 
                    'Banknote': 'bank',
                    'Scan': 'scanner'
                };
                
                updateTestResults(resultsId, '\n--- 问题图标别名测试 ---');
                Object.entries(problemIcons).forEach(([aliasName, originalName]) => {
                    if (lucide[aliasName]) {
                        updateTestResults(resultsId, `✓ 找到别名图标: ${aliasName} (用于 ${originalName})`);
                    } else {
                        updateTestResults(resultsId, `✗ 未找到别名图标: ${aliasName} (用于 ${originalName})`);
                    }
                });
                
            } else {
                updateTestResults(resultsId, '✗ Lucide图标库未加载');
            }
        }

        // 2. 测试问题图标
        function testProblemIcons() {
            const resultsId = 'problem-icons-results';
            const containerId = 'problem-icons-container';
            
            document.getElementById(resultsId).textContent = '';
            document.getElementById(containerId).innerHTML = '';
            
            updateTestResults(resultsId, '=== 问题图标测试 ===');
            
            const problemIcons = ['stop', 'shop', 'bank', 'scanner'];
            
            problemIcons.forEach(iconName => {
                try {
                    const svg = renderLucideIconForSettings(iconName, 24);
                    
                    // 创建图标显示容器
                    const iconDiv = document.createElement('div');
                    iconDiv.className = 'icon-test';
                    iconDiv.innerHTML = svg + '<br>' + iconName;
                    document.getElementById(containerId).appendChild(iconDiv);
                    
                    updateTestResults(resultsId, `✓ 成功渲染图标: ${iconName}`);
                } catch (error) {
                    updateTestResults(resultsId, `✗ 渲染图标失败: ${iconName} - ${error.message}`);
                }
            });
        }

        // 3. 测试图标渲染函数
        function testIconRendering() {
            const resultsId = 'icon-rendering-results';
            const containerId = 'icon-rendering-container';
            
            document.getElementById(resultsId).textContent = '';
            document.getElementById(containerId).innerHTML = '';
            
            updateTestResults(resultsId, '=== 图标渲染函数测试 ===');
            
            const testIcons = ['star', 'heart', 'home', 'user', 'stop', 'shop', 'bank', 'scanner'];
            
            testIcons.forEach(iconName => {
                try {
                    const svg = renderLucideIconForSettings(iconName, 24);
                    
                    // 创建图标显示容器
                    const iconDiv = document.createElement('div');
                    iconDiv.className = 'icon-test';
                    iconDiv.innerHTML = svg + '<br>' + iconName;
                    document.getElementById(containerId).appendChild(iconDiv);
                    
                    updateTestResults(resultsId, `✓ 成功渲染图标: ${iconName}`);
                } catch (error) {
                    updateTestResults(resultsId, `✗ 渲染图标失败: ${iconName} - ${error.message}`);
                }
            });
        }

        // 复制渲染函数用于测试
        function renderLucideIconForSettings(iconName, size = 16) {
            try {
                // 检查Lucide是否可用
                if (typeof lucide === 'undefined') {
                    console.warn('[Test] Lucide library not available');
                    return `<svg width="${size}" height="${size}" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>`;
                }

                // 转换图标名称为PascalCase（Lucide的命名约定）
                const pascalCaseName = iconName.split('-').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                ).join('');

                // 检查图标是否存在
                if (!lucide[pascalCaseName]) {
                    console.warn(`[Test] Lucide icon "${iconName}" (${pascalCaseName}) not found`);
                    // 尝试一些常见的别名映射
                    const aliasMap = {
                        'stop': 'CircleStop',
                        'shop': 'ShoppingBag', 
                        'bank': 'Banknote',
                        'scanner': 'Scan'
                    };
                    
                    const aliasName = aliasMap[iconName];
                    if (aliasName && lucide[aliasName]) {
                        console.log(`[Test] Using alias "${aliasName}" for "${iconName}"`);
                        const iconData = lucide[aliasName];
                        if (iconData && Array.isArray(iconData)) {
                            return renderIconFromData(iconData, size);
                        }
                    }
                    
                    return `<svg width="${size}" height="${size}" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>`;
                }

                // 直接使用Lucide图标数据创建SVG
                const iconData = lucide[pascalCaseName];
                if (iconData && Array.isArray(iconData)) {
                    return renderIconFromData(iconData, size);
                }

                // 如果以上方法都失败，返回默认图标
                return `<svg width="${size}" height="${size}" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>`;
            } catch (error) {
                console.error('[Test] Error rendering Lucide icon:', error);
                return `<svg width="${size}" height="${size}" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>`;
            }
        }

        // 从Lucide图标数据渲染SVG
        function renderIconFromData(iconData, size = 16) {
            let svgContent = '';
            iconData.forEach(([tag, attrs]) => {
                if (tag === 'path') {
                    svgContent += `<path d="${attrs.d}"${attrs.fill ? ` fill="${attrs.fill}"` : ''}${attrs.stroke ? ` stroke="${attrs.stroke}"` : ''}/>`;
                } else if (tag === 'circle') {
                    svgContent += `<circle cx="${attrs.cx}" cy="${attrs.cy}" r="${attrs.r}"${attrs.fill ? ` fill="${attrs.fill}"` : ''}${attrs.stroke ? ` stroke="${attrs.stroke}"` : ''}/>`;
                } else if (tag === 'rect') {
                    svgContent += `<rect x="${attrs.x}" y="${attrs.y}" width="${attrs.width}" height="${attrs.height}"${attrs.rx ? ` rx="${attrs.rx}"` : ''}${attrs.fill ? ` fill="${attrs.fill}"` : ''}${attrs.stroke ? ` stroke="${attrs.stroke}"` : ''}/>`;
                } else if (tag === 'line') {
                    svgContent += `<line x1="${attrs.x1}" y1="${attrs.y1}" x2="${attrs.x2}" y2="${attrs.y2}"${attrs.stroke ? ` stroke="${attrs.stroke}"` : ''}/>`;
                } else if (tag === 'polyline') {
                    svgContent += `<polyline points="${attrs.points}"${attrs.fill ? ` fill="${attrs.fill}"` : ''}${attrs.stroke ? ` stroke="${attrs.stroke}"` : ''}/>`;
                }
            });

            return `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                ${svgContent}
            </svg>`;
        }
    </script>
</body>
</html>
